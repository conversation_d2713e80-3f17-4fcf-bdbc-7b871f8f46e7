=== WinCBT-Admin Error Log ===
Started: 2025-05-23 16:00:59
----------------------------------------
2025-05-23 16:00:59 [INFO] Error handler initialized
2025-05-23 16:00:59 [INFO] Created logs directory
2025-05-23 16:00:59 [INFO] Error handler initialized
2025-05-23 16:00:59 [INFO] Created Database images directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\img
2025-05-23 16:00:59 [INFO] Created Candidates images directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\img\candidates\
2025-05-23 16:01:00 [INFO] Created Fingerprint templates directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\fpt\
2025-05-23 16:01:00 [INFO] Created Temporary files directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\tmp
2025-05-23 16:01:00 [INFO] Created Reports directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\reports
2025-05-23 16:01:00 [INFO] Created Backup directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\backup
2025-05-23 16:01:00 [INFO] Created empty Configuration file: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\config.ini
2025-05-23 16:01:00 [INFO] Created empty Candidates database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\candidates.ini
2025-05-23 16:01:00 [INFO] Created empty Hardware database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\hardware.ini
2025-05-23 16:01:00 [INFO] Created empty Rooms database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\rooms.ini
2025-05-23 16:01:00 [INFO] Created empty Seat assignments database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\seat_assignments.ini
2025-05-23 16:01:00 [INFO] Created empty Operators database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators.ini
2025-05-23 16:01:00 [INFO] Error handler initialized
2025-05-23 16:01:00 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-23 16:01:00 [INFO] Validating required files and directories
2025-05-23 16:01:00 [INFO] Validated directory: Database
2025-05-23 16:01:00 [INFO] Validated directory: Logs
2025-05-23 16:01:00 [WARNING] Temporary files directory not found at: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\tmp
2025-05-23 16:01:00 [INFO] Created directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\tmp
2025-05-23 16:01:00 [INFO] Validated directory: Database images
2025-05-23 16:01:00 [INFO] Validated directory: Candidate images
2025-05-23 16:01:00 [INFO] Validated directory: Fingerprint templates
2025-05-23 16:01:00 [INFO] Validated directory: Reports
2025-05-23 16:01:00 [INFO] Validated directory: Backup
2025-05-23 16:01:00 [WARNING] Company logo not found, creating placeholder
2025-05-23 16:01:00 [INFO] Created empty company logo placeholder
2025-05-23 16:01:00 [WARNING] Configuration file not found, creating default
2025-05-23 16:01:00 [INFO] All required files and directories validated successfully
2025-05-23 16:01:00 [INFO] Initializing application components
2025-05-23 16:01:00 [INFO] Loaded 0 candidates into cache
2025-05-23 16:01:00 [INFO] Loaded 0 hardware entries into cache
2025-05-23 16:01:00 [INFO] Loaded 0 rooms into cache
2025-05-23 16:01:00 [INFO] Loaded 0 seat assignments into cache
2025-05-23 16:01:00 [INFO] Loaded 0 operators into cache
2025-05-23 16:01:00 [INFO] Database manager initialized successfully
2025-05-23 16:01:04 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 13:01:19 [INFO] Error handler initialized
2025-05-24 13:01:19 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 13:01:19 [INFO] Validating required files and directories
2025-05-24 13:01:19 [INFO] Validated directory: Database
2025-05-24 13:01:19 [INFO] Validated directory: Logs
2025-05-24 13:01:19 [INFO] Validated directory: Temporary files
2025-05-24 13:01:19 [INFO] Validated directory: Database images
2025-05-24 13:01:19 [INFO] Validated directory: Candidate images
2025-05-24 13:01:19 [INFO] Validated directory: Fingerprint templates
2025-05-24 13:01:19 [INFO] Validated directory: Reports
2025-05-24 13:01:19 [INFO] Validated directory: Backup
2025-05-24 13:01:19 [INFO] All required files and directories validated successfully
2025-05-24 13:01:19 [INFO] Initializing application components
2025-05-24 13:01:19 [INFO] Loaded 0 candidates into cache
2025-05-24 13:01:19 [INFO] Loaded 0 hardware entries into cache
2025-05-24 13:01:19 [INFO] Loaded 0 rooms into cache
2025-05-24 13:01:19 [INFO] Loaded 0 seat assignments into cache
2025-05-24 13:01:19 [INFO] Loaded 0 operators into cache
2025-05-24 13:01:19 [INFO] Database manager initialized successfully
2025-05-24 13:01:31 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 13:44:24 [INFO] Error handler initialized
2025-05-24 13:44:24 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 13:44:24 [INFO] Validating required files and directories
2025-05-24 13:44:24 [INFO] Validated directory: Database
2025-05-24 13:44:24 [INFO] Validated directory: Logs
2025-05-24 13:44:24 [INFO] Validated directory: Temporary files
2025-05-24 13:44:24 [INFO] Validated directory: Database images
2025-05-24 13:44:24 [INFO] Validated directory: Candidate images
2025-05-24 13:44:24 [INFO] Validated directory: Fingerprint templates
2025-05-24 13:44:24 [INFO] Validated directory: Reports
2025-05-24 13:44:24 [INFO] Validated directory: Backup
2025-05-24 13:44:24 [INFO] All required files and directories validated successfully
2025-05-24 13:44:24 [INFO] Initializing application components
2025-05-24 13:44:24 [INFO] Loaded 0 candidates into cache
2025-05-24 13:44:24 [INFO] Loaded 0 hardware entries into cache
2025-05-24 13:44:24 [INFO] Loaded 0 rooms into cache
2025-05-24 13:44:24 [INFO] Loaded 0 seat assignments into cache
2025-05-24 13:44:24 [INFO] Loaded 0 operators into cache
2025-05-24 13:44:24 [INFO] Database manager initialized successfully
2025-05-24 14:04:50 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 14:07:04 [INFO] Error handler initialized
2025-05-24 14:07:04 [INFO] Created empty Operators database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators.ini
2025-05-24 14:07:04 [INFO] Error handler initialized
2025-05-24 14:07:04 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:07:04 [INFO] Validating required files and directories
2025-05-24 14:07:04 [INFO] Validated directory: Database
2025-05-24 14:07:04 [INFO] Validated directory: Logs
2025-05-24 14:07:04 [INFO] Validated directory: Temporary files
2025-05-24 14:07:04 [INFO] Validated directory: Database images
2025-05-24 14:07:04 [INFO] Validated directory: Candidate images
2025-05-24 14:07:04 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:07:04 [INFO] Validated directory: Reports
2025-05-24 14:07:04 [INFO] Validated directory: Backup
2025-05-24 14:07:04 [WARNING] Configuration file not found, creating default
2025-05-24 14:07:04 [INFO] All required files and directories validated successfully
2025-05-24 14:07:04 [INFO] Initializing application components
2025-05-24 14:07:04 [CRITICAL] Failed to initialize database manager: This value of type "Class" has no method named "new".
2025-05-24 14:07:04 [ERROR] Failed to initialize database manager: This value of type "Class" has no method named "new".
2025-05-24 14:07:08 [CRITICAL] Failed to initialize operator manager: This value of type "Class" has no method named "new".
2025-05-24 14:07:08 [ERROR] Failed to initialize operator manager: This value of type "Class" has no method named "new".
2025-05-24 14:07:09 [ERROR] Failed to update operator statistics: This value of type "Class" has no method named "new".
2025-05-24 14:07:16 [ERROR] Failed to load operators: This value of type "String" has no method named "GetAllOperators".
2025-05-24 14:07:23 [ERROR] Failed to load operators: This value of type "String" has no method named "GetAllOperators".
2025-05-24 14:07:28 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 14:12:15 [INFO] Error handler initialized
2025-05-24 14:12:15 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:12:15 [INFO] Validating required files and directories
2025-05-24 14:12:15 [INFO] Validated directory: Database
2025-05-24 14:12:15 [INFO] Validated directory: Logs
2025-05-24 14:12:15 [INFO] Validated directory: Temporary files
2025-05-24 14:12:15 [INFO] Validated directory: Database images
2025-05-24 14:12:15 [INFO] Validated directory: Candidate images
2025-05-24 14:12:15 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:12:15 [INFO] Validated directory: Reports
2025-05-24 14:12:15 [INFO] Validated directory: Backup
2025-05-24 14:12:15 [INFO] All required files and directories validated successfully
2025-05-24 14:12:15 [INFO] Initializing application components
2025-05-24 14:12:15 [INFO] Loaded 0 candidates into cache
2025-05-24 14:12:15 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:12:15 [INFO] Loaded 0 rooms into cache
2025-05-24 14:12:15 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:12:15 [INFO] Loaded 0 operators into cache
2025-05-24 14:12:15 [INFO] Database manager initialized successfully
2025-05-24 14:12:15 [INFO] Operator manager initialized successfully
2025-05-24 14:12:36 [ERROR] Failed to load operators: This value of type "String" has no method named "SetText".
2025-05-24 14:12:51 [INFO] Application exiting: Single (Code: 0)
2025-05-24 14:12:51 [INFO] Error handler initialized
2025-05-24 14:12:51 [INFO] Created empty Operators database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators.ini
2025-05-24 14:12:51 [INFO] Error handler initialized
2025-05-24 14:12:51 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:12:51 [INFO] Validating required files and directories
2025-05-24 14:12:51 [INFO] Validated directory: Database
2025-05-24 14:12:51 [INFO] Validated directory: Logs
2025-05-24 14:12:51 [INFO] Validated directory: Temporary files
2025-05-24 14:12:51 [INFO] Validated directory: Database images
2025-05-24 14:12:51 [INFO] Validated directory: Candidate images
2025-05-24 14:12:51 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:12:51 [INFO] Validated directory: Reports
2025-05-24 14:12:51 [INFO] Validated directory: Backup
2025-05-24 14:12:51 [INFO] All required files and directories validated successfully
2025-05-24 14:12:51 [INFO] Initializing application components
2025-05-24 14:12:51 [INFO] Loaded 0 candidates into cache
2025-05-24 14:12:51 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:12:51 [INFO] Loaded 0 rooms into cache
2025-05-24 14:12:51 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:12:51 [INFO] Loaded 0 operators into cache
2025-05-24 14:12:51 [INFO] Database manager initialized successfully
2025-05-24 14:12:51 [INFO] Operator manager initialized successfully
2025-05-24 14:13:06 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 14:13:48 [INFO] Error handler initialized
2025-05-24 14:13:48 [INFO] Created empty Operators database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators.ini
2025-05-24 14:13:48 [INFO] Error handler initialized
2025-05-24 14:13:48 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:13:48 [INFO] Validating required files and directories
2025-05-24 14:13:48 [INFO] Validated directory: Database
2025-05-24 14:13:48 [INFO] Validated directory: Logs
2025-05-24 14:13:48 [INFO] Validated directory: Temporary files
2025-05-24 14:13:48 [INFO] Validated directory: Database images
2025-05-24 14:13:48 [INFO] Validated directory: Candidate images
2025-05-24 14:13:48 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:13:48 [INFO] Validated directory: Reports
2025-05-24 14:13:48 [INFO] Validated directory: Backup
2025-05-24 14:13:48 [INFO] All required files and directories validated successfully
2025-05-24 14:13:48 [INFO] Initializing application components
2025-05-24 14:13:48 [INFO] Loaded 0 candidates into cache
2025-05-24 14:13:48 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:13:48 [INFO] Loaded 0 rooms into cache
2025-05-24 14:13:48 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:13:48 [INFO] Loaded 0 operators into cache
2025-05-24 14:13:48 [INFO] Database manager initialized successfully
2025-05-24 14:13:48 [INFO] Created default admin account
2025-05-24 14:13:48 [INFO] Operator manager initialized successfully
2025-05-24 14:14:56 [INFO] Application exiting: Single (Code: 0)
2025-05-24 14:14:56 [INFO] Error handler initialized
2025-05-24 14:14:56 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:14:56 [INFO] Validating required files and directories
2025-05-24 14:14:56 [INFO] Validated directory: Database
2025-05-24 14:14:56 [INFO] Validated directory: Logs
2025-05-24 14:14:56 [INFO] Validated directory: Temporary files
2025-05-24 14:14:56 [INFO] Validated directory: Database images
2025-05-24 14:14:56 [INFO] Validated directory: Candidate images
2025-05-24 14:14:57 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:14:57 [INFO] Validated directory: Reports
2025-05-24 14:14:57 [INFO] Validated directory: Backup
2025-05-24 14:14:57 [INFO] All required files and directories validated successfully
2025-05-24 14:14:57 [INFO] Initializing application components
2025-05-24 14:14:57 [INFO] Loaded 0 candidates into cache
2025-05-24 14:14:57 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:14:57 [INFO] Loaded 0 rooms into cache
2025-05-24 14:14:57 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:14:57 [INFO] Loaded 1 operators into cache
2025-05-24 14:14:57 [INFO] Database manager initialized successfully
2025-05-24 14:14:57 [INFO] Operator manager initialized successfully
2025-05-24 14:15:09 [ERROR] Failed to load operators: This value of type "String" has no method named "SetText".
2025-05-24 14:15:30 [ERROR] Failed to load operators: This value of type "String" has no method named "SetText".
2025-05-24 14:16:13 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 14:16:24 [INFO] Error handler initialized
2025-05-24 14:16:24 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:16:24 [INFO] Debug mode enabled
2025-05-24 14:16:24 [INFO] Validating required files and directories
2025-05-24 14:16:24 [INFO] Validated directory: Database
2025-05-24 14:16:24 [INFO] Validated directory: Logs
2025-05-24 14:16:24 [INFO] Validated directory: Temporary files
2025-05-24 14:16:24 [INFO] Validated directory: Database images
2025-05-24 14:16:24 [INFO] Validated directory: Candidate images
2025-05-24 14:16:24 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:16:24 [INFO] Validated directory: Reports
2025-05-24 14:16:24 [INFO] Validated directory: Backup
2025-05-24 14:16:24 [INFO] All required files and directories validated successfully
2025-05-24 14:16:24 [INFO] Initializing application components
2025-05-24 14:16:24 [INFO] Loaded 0 candidates into cache
2025-05-24 14:16:24 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:16:24 [INFO] Loaded 0 rooms into cache
2025-05-24 14:16:24 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:16:24 [INFO] Loaded 1 operators into cache
2025-05-24 14:16:24 [INFO] Database manager initialized successfully
2025-05-24 14:16:24 [INFO] Operator manager initialized successfully
2025-05-24 14:16:36 [ERROR] Failed to load operators: This value of type "String" has no method named "SetText".
2025-05-24 14:19:11 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 14:19:39 [INFO] Error handler initialized
2025-05-24 14:19:39 [INFO] Created empty Operators database: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators.ini
2025-05-24 14:19:39 [INFO] Error handler initialized
2025-05-24 14:19:39 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:19:39 [INFO] Validating required files and directories
2025-05-24 14:19:39 [INFO] Validated directory: Database
2025-05-24 14:19:39 [INFO] Validated directory: Logs
2025-05-24 14:19:39 [INFO] Validated directory: Temporary files
2025-05-24 14:19:39 [INFO] Validated directory: Database images
2025-05-24 14:19:39 [INFO] Validated directory: Candidate images
2025-05-24 14:19:39 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:19:39 [INFO] Validated directory: Reports
2025-05-24 14:19:39 [INFO] Validated directory: Backup
2025-05-24 14:19:39 [INFO] All required files and directories validated successfully
2025-05-24 14:19:39 [INFO] Initializing application components
2025-05-24 14:19:39 [INFO] Loaded 0 candidates into cache
2025-05-24 14:19:39 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:19:39 [INFO] Loaded 0 rooms into cache
2025-05-24 14:19:39 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:19:39 [INFO] Loaded 0 operators into cache
2025-05-24 14:19:39 [INFO] Database manager initialized successfully
2025-05-24 14:19:39 [INFO] Created default admin account
2025-05-24 14:19:39 [INFO] Operator manager initialized successfully
2025-05-24 14:20:18 [INFO] Application exiting: Single (Code: 0)
2025-05-24 14:20:19 [INFO] Error handler initialized
2025-05-24 14:20:19 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:20:19 [INFO] Validating required files and directories
2025-05-24 14:20:19 [INFO] Validated directory: Database
2025-05-24 14:20:19 [INFO] Validated directory: Logs
2025-05-24 14:20:19 [INFO] Validated directory: Temporary files
2025-05-24 14:20:19 [INFO] Validated directory: Database images
2025-05-24 14:20:19 [INFO] Validated directory: Candidate images
2025-05-24 14:20:19 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:20:19 [INFO] Validated directory: Reports
2025-05-24 14:20:19 [INFO] Validated directory: Backup
2025-05-24 14:20:19 [INFO] All required files and directories validated successfully
2025-05-24 14:20:19 [INFO] Initializing application components
2025-05-24 14:20:19 [INFO] Loaded 0 candidates into cache
2025-05-24 14:20:19 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:20:19 [INFO] Loaded 0 rooms into cache
2025-05-24 14:20:19 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:20:19 [INFO] Loaded 1 operators into cache
2025-05-24 14:20:19 [INFO] Database manager initialized successfully
2025-05-24 14:20:19 [INFO] Operator manager initialized successfully
2025-05-24 14:35:45 [INFO] Application exiting: Single (Code: 0)
2025-05-24 14:35:45 [INFO] Error handler initialized
2025-05-24 14:35:45 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:35:45 [INFO] Validating required files and directories
2025-05-24 14:35:45 [INFO] Validated directory: Database
2025-05-24 14:35:45 [INFO] Validated directory: Logs
2025-05-24 14:35:45 [INFO] Validated directory: Temporary files
2025-05-24 14:35:45 [INFO] Validated directory: Database images
2025-05-24 14:35:45 [INFO] Validated directory: Candidate images
2025-05-24 14:35:45 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:35:45 [INFO] Validated directory: Reports
2025-05-24 14:35:45 [INFO] Validated directory: Backup
2025-05-24 14:35:45 [INFO] All required files and directories validated successfully
2025-05-24 14:35:45 [INFO] Initializing application components
2025-05-24 14:35:45 [INFO] Loaded 0 candidates into cache
2025-05-24 14:35:45 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:35:45 [INFO] Loaded 0 rooms into cache
2025-05-24 14:35:45 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:35:45 [INFO] Loaded 1 operators into cache
2025-05-24 14:35:45 [INFO] Database manager initialized successfully
2025-05-24 14:35:45 [INFO] Operator manager initialized successfully
2025-05-24 14:35:45 [INFO] Created operators directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators
2025-05-24 14:35:45 [INFO] Created photos directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators\photos
2025-05-24 14:35:45 [INFO] Created fingerprints directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators\fingerprints
2025-05-24 14:35:45 [INFO] Created operator logs directory: E:\Sync\Sanbroz\WORK\CBT\WinCBT-Admin\db\operators\logs
2025-05-24 14:42:58 [INFO] Error handler initialized
2025-05-24 14:42:58 [INFO] Added new operator: test_biometric
2025-05-24 14:42:58 [INFO] Deleted operator: test_biometric
2025-05-24 14:43:39 [INFO] Application exiting: Single (Code: 0)
2025-05-24 14:43:39 [INFO] Error handler initialized
2025-05-24 14:43:39 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:43:39 [INFO] Debug mode enabled
2025-05-24 14:43:39 [INFO] Validating required files and directories
2025-05-24 14:43:39 [INFO] Validated directory: Database
2025-05-24 14:43:39 [INFO] Validated directory: Logs
2025-05-24 14:43:39 [INFO] Validated directory: Temporary files
2025-05-24 14:43:39 [INFO] Validated directory: Database images
2025-05-24 14:43:39 [INFO] Validated directory: Candidate images
2025-05-24 14:43:39 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:43:39 [INFO] Validated directory: Reports
2025-05-24 14:43:39 [INFO] Validated directory: Backup
2025-05-24 14:43:39 [INFO] All required files and directories validated successfully
2025-05-24 14:43:39 [INFO] Initializing application components
2025-05-24 14:43:39 [INFO] Loaded 0 candidates into cache
2025-05-24 14:43:39 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:43:39 [INFO] Loaded 0 rooms into cache
2025-05-24 14:43:39 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:43:39 [INFO] Loaded 1 operators into cache
2025-05-24 14:43:39 [INFO] Database manager initialized successfully
2025-05-24 14:43:39 [INFO] Operator manager initialized successfully
2025-05-24 14:43:48 [INFO] Application exiting: Exit (Code: 0)
2025-05-24 14:43:53 [INFO] Error handler initialized
2025-05-24 14:43:53 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:43:53 [INFO] Validating required files and directories
2025-05-24 14:43:53 [INFO] Validated directory: Database
2025-05-24 14:43:53 [INFO] Validated directory: Logs
2025-05-24 14:43:53 [INFO] Validated directory: Temporary files
2025-05-24 14:43:53 [INFO] Validated directory: Database images
2025-05-24 14:43:54 [INFO] Validated directory: Candidate images
2025-05-24 14:43:54 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:43:54 [INFO] Validated directory: Reports
2025-05-24 14:43:54 [INFO] Validated directory: Backup
2025-05-24 14:43:54 [INFO] All required files and directories validated successfully
2025-05-24 14:43:54 [INFO] Initializing application components
2025-05-24 14:43:54 [INFO] Loaded 0 candidates into cache
2025-05-24 14:43:54 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:43:54 [INFO] Loaded 0 rooms into cache
2025-05-24 14:43:54 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:43:54 [INFO] Loaded 1 operators into cache
2025-05-24 14:43:54 [INFO] Database manager initialized successfully
2025-05-24 14:43:54 [INFO] Operator manager initialized successfully
2025-05-24 14:44:19 [INFO] Error handler initialized
2025-05-24 14:44:19 [INFO] Starting WinCBT-Admin (v1.0.0 Build ********)
2025-05-24 14:44:19 [INFO] Validating required files and directories
2025-05-24 14:44:19 [INFO] Validated directory: Database
2025-05-24 14:44:19 [INFO] Validated directory: Logs
2025-05-24 14:44:19 [INFO] Validated directory: Temporary files
2025-05-24 14:44:19 [INFO] Validated directory: Database images
2025-05-24 14:44:19 [INFO] Validated directory: Candidate images
2025-05-24 14:44:19 [INFO] Validated directory: Fingerprint templates
2025-05-24 14:44:19 [INFO] Validated directory: Reports
2025-05-24 14:44:19 [INFO] Validated directory: Backup
2025-05-24 14:44:19 [INFO] All required files and directories validated successfully
2025-05-24 14:44:19 [INFO] Initializing application components
2025-05-24 14:44:19 [INFO] Loaded 0 candidates into cache
2025-05-24 14:44:19 [INFO] Loaded 0 hardware entries into cache
2025-05-24 14:44:19 [INFO] Loaded 0 rooms into cache
2025-05-24 14:44:19 [INFO] Loaded 0 seat assignments into cache
2025-05-24 14:44:19 [INFO] Loaded 1 operators into cache
2025-05-24 14:44:19 [INFO] Database manager initialized successfully
2025-05-24 14:44:19 [INFO] Operator manager initialized successfully
2025-05-24 14:44:59 [ERROR] Fingerprint capture error: CaptureTemplate failed: CaptureImage failed: Fingerprint capture timed out. Please try again.
