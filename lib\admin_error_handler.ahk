#Requires AutoHotkey v2.0

; WinCBT-Admin <PERSON>rro<PERSON>
; Provides centralized error handling and validation functions

class ErrorHandler {
    static logFile := A_ScriptDir "\logs\admin_error.log"
    static initialized := false
    static statusCallback := 0
    static debugMode := false

    ; ; Initialize()
    ; ; Sets up the error handler, creates log directories, and initializes the log file.
    ; ; @param statusCallback: Optional callback function to receive status messages.
    ; ; @param debugMode: Whether to enable debug mode for more verbose logging.
    static Initialize(statusCallback := 0, debugMode := false) {
        ; Set properties
        this.statusCallback := statusCallback
        this.debugMode := debugMode

        ; Create logs directory if it doesn't exist
        if (!DirExist(A_ScriptDir "\logs")) {
            try {
                DirCreate(A_ScriptDir "\logs")
                this.LogMessage("INFO", "Created logs directory")
            } catch as err {
                this.LogMessage("ERROR", "Failed to create logs directory: " err.Message)
                this.ShowError("Failed to create logs directory. The application may not function correctly.")
            }
        }

        ; Initialize log file with header if it doesn't exist
        if (!FileExist(this.logFile)) {
            try {
                FileAppend("=== WinCBT-Admin Error Log ===`n"
                    . "Started: " FormatTime(, "yyyy-MM-dd HH:mm:ss") "`n"
                    . "----------------------------------------`n", this.logFile)
            } catch as err {
                ; If we can't write to the log file, show an error but continue
                this.ShowError("Failed to initialize error log: " err.Message)
            }
        }

        this.initialized := true
        this.LogMessage("INFO", "Error handler initialized")
    }

    ; ; LogMessage(level, message)
    ; ; Logs a message to the error log file with timestamp and severity level.
    ; ; @param level: The severity level (e.g., "INFO", "WARNING", "ERROR", "CRITICAL").
    ; ; @param message: The message to log.
    static LogMessage(level, message) {
        ; Initialize if not already done
        if (!this.initialized)
            this.Initialize()

        ; Format the log entry
        timestamp := FormatTime(, "yyyy-MM-dd HH:mm:ss")
        logEntry := timestamp " [" level "] " message "`n"

        ; Write to log file
        try {
            FileAppend(logEntry, this.logFile)
        } catch as err {
            ; If we can't write to the log, output to debug console
            OutputDebug("Failed to write to log file: " err.Message)
            OutputDebug(logEntry)
        }

        ; Output to debug console in debug mode
        if (this.debugMode)
            OutputDebug(logEntry)

        ; Call status callback if provided
        if (this.statusCallback && (level == "ERROR" || level == "CRITICAL"))
            this.statusCallback.Call(message)
    }

    ; ; ShowError(message, title := "Error", options := "Icon! +AlwaysOnTop", forceShow := true)
    ; ; Displays an error message box and logs the error.
    ; ; @param message: The error message to display.
    ; ; @param title: The title of the message box.
    ; ; @param options: Options for the message box (see AHK MsgBox documentation).
    ; ; @param forceShow: Whether to force showing the message box (set to false to only log).
    static ShowError(message, title := "Error", options := "Icon! +AlwaysOnTop", forceShow := true) {
        ; Log the error
        this.LogMessage("ERROR", message)

        ; Ensure AlwaysOnTop is included in options if not already present
        if (!InStr(options, "+AlwaysOnTop")) {
            options .= " +AlwaysOnTop"
        }

        ; Show message box only if forceShow is true or in debug mode
        if (forceShow || this.debugMode) {
            MsgBox(message, title, options)
        }
    }

    ; ; ValidateFile(filePath, description, critical := false, createIfMissing := false)
    ; ; Validates that a file exists and is accessible.
    ; ; @param filePath: The path to the file to validate.
    ; ; @param description: A description of the file for error messages.
    ; ; @param critical: Whether the file is critical for application function.
    ; ; @param createIfMissing: Whether to create an empty file if it doesn't exist.
    ; ; @return: True if the file exists and is accessible, False otherwise.
    static ValidateFile(filePath, description, critical := false, createIfMissing := false) {
        if (!FileExist(filePath)) {
            ; Log the missing file
            this.LogMessage(critical ? "CRITICAL" : "WARNING", description " not found at: " filePath)

            ; Create the file if requested
            if (createIfMissing) {
                try {
                    ; Create the directory structure if needed
                    SplitPath(filePath, , &dir)
                    if (dir && !DirExist(dir)) {
                        DirCreate(dir)
                        this.LogMessage("INFO", "Created directory: " dir)
                    }

                    ; Create an empty file
                    FileAppend("", filePath)
                    this.LogMessage("INFO", "Created empty file: " filePath)
                    return true
                } catch as err {
                    this.LogMessage("ERROR", "Failed to create " description ": " err.Message)
                    if (critical)
                        this.ShowError("Failed to create " description " at " filePath)
                    return false
                }
            } else {
                ; Show error for critical files
                if (critical)
                    this.ShowError(description " not found at: " filePath)
                return false
            }
        }

        ; Just check if the file exists - don't try to read it
        ; This avoids "Invalid option" errors when trying to read certain file types
        if (FileExist(filePath)) {
            this.LogMessage("INFO", "Validated file: " description)
            return true
        } else {
            this.LogMessage("ERROR", "Cannot access " description ": File does not exist")
            if (critical)
                this.ShowError("Cannot access " description " at " filePath)
            return false
        }
    }

    ; ; ValidateDirectory(dirPath, description, critical := false, createIfMissing := true)
    ; ; Validates that a directory exists and is accessible.
    ; ; @param dirPath: The path to the directory to validate.
    ; ; @param description: A description of the directory for error messages.
    ; ; @param critical: Whether the directory is critical for application function.
    ; ; @param createIfMissing: Whether to create the directory if it doesn't exist.
    ; ; @return: True if the directory exists and is accessible, False otherwise.
    static ValidateDirectory(dirPath, description, critical := false, createIfMissing := true) {
        if (!DirExist(dirPath)) {
            ; Log the missing directory
            this.LogMessage(critical ? "CRITICAL" : "WARNING", description " directory not found at: " dirPath)

            ; Create the directory if requested
            if (createIfMissing) {
                try {
                    DirCreate(dirPath)
                    this.LogMessage("INFO", "Created directory: " dirPath)
                    return true
                } catch as err {
                    this.LogMessage("ERROR", "Failed to create " description " directory: " err.Message)
                    if (critical)
                        this.ShowError("Failed to create " description " directory at " dirPath)
                    return false
                }
            } else {
                ; Show error for critical directories
                if (critical)
                    this.ShowError(description " directory not found at: " dirPath)
                return false
            }
        }

        ; Just check if the directory exists - don't try to list files
        ; This avoids potential permission issues or other errors
        if (DirExist(dirPath)) {
            this.LogMessage("INFO", "Validated directory: " description)
            return true
        } else {
            this.LogMessage("ERROR", "Cannot access " description " directory: Directory does not exist")
            if (critical)
                this.ShowError("Cannot access " description " directory at " dirPath)
            return false
        }
    }

    ; ; ValidateRequiredFiles()
    ; ; Validates all required files and directories for the application.
    ; ; Creates missing directories and logs any issues.
    ; ; @return: True if all critical files and directories are available, False otherwise.
    static ValidateRequiredFiles() {
        allValid := true
        missingItems := 0

        this.LogMessage("INFO", "Validating required files and directories")

        ; Validate critical directories
        allValid := this.ValidateDirectory(A_ScriptDir "\db", "Database", true) && allValid
        allValid := this.ValidateDirectory(A_ScriptDir "\logs", "Logs", true) && allValid
        allValid := this.ValidateDirectory(A_ScriptDir "\tmp", "Temporary files", false) && allValid

        ; Validate subdirectories
        allValid := this.ValidateDirectory(A_ScriptDir "\db\img", "Database images", true) && allValid
        allValid := this.ValidateDirectory(A_ScriptDir "\db\img\candidates", "Candidate images", true) && allValid
        allValid := this.ValidateDirectory(A_ScriptDir "\db\fpt", "Fingerprint templates", true) && allValid
        allValid := this.ValidateDirectory(A_ScriptDir "\db\reports", "Reports", true) && allValid
        allValid := this.ValidateDirectory(A_ScriptDir "\db\backup", "Backup", true) && allValid

        ; Check for company logo and create a placeholder if it doesn't exist
        companyLogoPath := A_ScriptDir "\db\img\company.jpg"
        if (!FileExist(companyLogoPath)) {
            this.LogMessage("WARNING", "Company logo not found, creating placeholder")
            try {
                ; Create an empty file as placeholder
                FileAppend("", companyLogoPath)
                this.LogMessage("INFO", "Created empty company logo placeholder")
            } catch as err {
                this.LogMessage("WARNING", "Failed to create company logo placeholder: " err.Message)
            }
        }

        ; Validate critical files - create if missing
        if (!FileExist(A_ScriptDir "\config.ini")) {
            this.LogMessage("WARNING", "Configuration file not found, creating default")
            missingItems++
            try {
                FileAppend("", A_ScriptDir "\config.ini")
            } catch as err {
                this.LogMessage("ERROR", "Failed to create configuration file: " err.Message)
                allValid := false
            }
        }

        ; Validate database files - create if missing
        dbFiles := [
            {path: A_ScriptDir "\db\config.ini", desc: "Database configuration"},
            {path: A_ScriptDir "\db\candidates.ini", desc: "Candidates database"},
            {path: A_ScriptDir "\db\hardware.ini", desc: "Hardware configuration"},
            {path: A_ScriptDir "\db\rooms.ini", desc: "Rooms configuration"},
            {path: A_ScriptDir "\db\seat_assignments.ini", desc: "Seat assignments"},
            {path: A_ScriptDir "\db\operators.ini", desc: "Operators database"}
        ]

        for file in dbFiles {
            if (!FileExist(file.path)) {
                this.LogMessage("WARNING", file.desc " not found, creating empty file")
                missingItems++
                try {
                    FileAppend("", file.path)
                } catch as err {
                    this.LogMessage("ERROR", "Failed to create " file.desc ": " err.Message)
                    allValid := false
                }
            }
        }

        ; Log validation results
        if (allValid) {
            this.LogMessage("INFO", "All required files and directories validated successfully")
        } else {
            this.LogMessage("WARNING", "Validation found " missingItems " missing items")

            ; Only show a message box if there are critical missing items
            if (missingItems > 0) {
                this.ShowError("Some required files or directories are missing. The application may not function correctly.",
                              "Startup Warning", "Icon!", missingItems > 3)
            }
        }

        return allValid
    }
}
