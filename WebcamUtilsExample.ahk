#Requires AutoHotkey v2.0+
#SingleInstance Force

; Example script demonstrating improved webcam implementation
; This shows best practices for webcam initialization and capture

; Include the webcam utilities
#Include %A_ScriptDir%\lib\webcam_utils.ahk

; Create the main GUI
exampleGui := Gui("+Resize", "Webcam Utils Example - Improved Implementation")
exampleGui.SetFont("s10", "Segoe UI")

; Add title
exampleGui.Add("Text", "x10 y10 w580 h25 Center", "Webcam Utilities Example").SetFont("s12 Bold")

; Create webcam preview area
exampleGui.Add("GroupBox", "x10 y40 w580 h400", "Camera Preview")
webcamControl := exampleGui.Add("Text", "x20 y65 w560 h350 +Border +0x200", "No Camera Feed")
webcamControl.SetFont("s14")

; Create captured image area (initially hidden)
capturedImageControl := exampleGui.Add("Picture", "x20 y65 w560 h350 +Border Hidden", "")

; Status and control area
exampleGui.Add("GroupBox", "x10 y450 w580 h120", "Controls and Status")

; Status text
statusText := exampleGui.Add("Text", "x20 y475 w560 h20", "Status: Ready")

; Control buttons
startBtn := exampleGui.Add("Button", "x20 y505 w100 h30", "Start Camera")
stopBtn := exampleGui.Add("Button", "x130 y505 w100 h30", "Stop Camera")
captureBtn := exampleGui.Add("Button", "x240 y505 w100 h30", "Capture Image")
toggleBtn := exampleGui.Add("Button", "x350 y505 w120 h30", "Show Live Feed")
closeBtn := exampleGui.Add("Button", "x480 y505 w100 h30", "Close")

; Initially disable some buttons
stopBtn.Enabled := false
captureBtn.Enabled := false
toggleBtn.Enabled := false

; Global variables
webcamHandle := 0
capturedImagePath := ""
showingLiveFeed := true

; Function to update status
UpdateStatus(message) {
    statusText.Text := "Status: " message
    OutputDebug("WebcamExample: " message)
}

; Function to start webcam
StartWebcam(*) {
    try {
        UpdateStatus("Starting camera...")
        
        ; Stop any existing webcam first
        if (webcamHandle != 0) {
            StopWebcam(webcamHandle)
            webcamHandle := 0
        }
        
        ; Start the webcam
        webcamHandle := StartWebcam(webcamControl, "Default Camera")
        
        if (webcamHandle != 0) {
            UpdateStatus("Camera started successfully")
            startBtn.Enabled := false
            stopBtn.Enabled := true
            captureBtn.Enabled := true
            
            ; Show live feed
            showingLiveFeed := true
            ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)
            toggleBtn.Text := "Show Captured"
            toggleBtn.Enabled := (capturedImagePath != "")
        } else {
            UpdateStatus("Failed to start camera")
            MsgBox("Failed to start camera. Please check if camera is connected and not in use by another application.", "Camera Error", "Icon! +AlwaysOnTop")
        }
        
    } catch as err {
        UpdateStatus("Error starting camera: " err.Message)
        MsgBox("Error starting camera: " err.Message, "Error", "Icon! +AlwaysOnTop")
    }
}

; Function to stop webcam
StopWebcam(*) {
    try {
        if (webcamHandle != 0) {
            UpdateStatus("Stopping camera...")
            StopWebcam(webcamHandle)
            webcamHandle := 0
            
            UpdateStatus("Camera stopped")
            startBtn.Enabled := true
            stopBtn.Enabled := false
            captureBtn.Enabled := false
            
            ; Clear the webcam control
            webcamControl.Text := "No Camera Feed"
        }
        
    } catch as err {
        UpdateStatus("Error stopping camera: " err.Message)
        MsgBox("Error stopping camera: " err.Message, "Error", "Icon! +AlwaysOnTop")
    }
}

; Function to capture image
CaptureImage(*) {
    try {
        if (webcamHandle = 0) {
            UpdateStatus("No active camera to capture from")
            MsgBox("Please start the camera first.", "No Camera", "Icon! +AlwaysOnTop")
            return
        }
        
        UpdateStatus("Capturing image...")
        
        ; Capture the image
        result := CaptureWebcamImage(webcamControl, webcamHandle)
        
        if (result.success) {
            capturedImagePath := result.filename
            UpdateStatus("Image captured: " result.filename)
            
            ; Enable toggle button
            toggleBtn.Enabled := true
            
            ; Show the captured image
            capturedImageControl.Value := capturedImagePath
            ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, false)
            showingLiveFeed := false
            toggleBtn.Text := "Show Live Feed"
            
            MsgBox("Image captured successfully!`n`nSaved to: " result.filename, "Capture Success", "Icon! +AlwaysOnTop")
        } else {
            UpdateStatus("Failed to capture image: " result.error)
            MsgBox("Failed to capture image: " result.error, "Capture Error", "Icon! +AlwaysOnTop")
        }
        
    } catch as err {
        UpdateStatus("Error capturing image: " err.Message)
        MsgBox("Error capturing image: " err.Message, "Error", "Icon! +AlwaysOnTop")
    }
}

; Function to toggle between live feed and captured image
ToggleView(*) {
    try {
        if (capturedImagePath = "") {
            MsgBox("No captured image to show.", "No Image", "Icon! +AlwaysOnTop")
            return
        }
        
        showingLiveFeed := !showingLiveFeed
        ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, showingLiveFeed)
        
        if (showingLiveFeed) {
            toggleBtn.Text := "Show Captured"
            UpdateStatus("Showing live camera feed")
        } else {
            toggleBtn.Text := "Show Live Feed"
            UpdateStatus("Showing captured image")
        }
        
    } catch as err {
        UpdateStatus("Error toggling view: " err.Message)
        MsgBox("Error toggling view: " err.Message, "Error", "Icon! +AlwaysOnTop")
    }
}

; Function to close application
CloseApp(*) {
    try {
        ; Stop webcam if running
        if (webcamHandle != 0) {
            StopWebcam(webcamHandle)
        }
        
        ; Close the GUI
        exampleGui.Destroy()
        ExitApp()
        
    } catch as err {
        OutputDebug("Error during cleanup: " err.Message)
        ExitApp()
    }
}

; Event handlers
startBtn.OnEvent("Click", StartWebcam)
stopBtn.OnEvent("Click", StopWebcam)
captureBtn.OnEvent("Click", CaptureImage)
toggleBtn.OnEvent("Click", ToggleView)
closeBtn.OnEvent("Click", CloseApp)

; Handle GUI close
exampleGui.OnEvent("Close", CloseApp)

; Show the GUI
exampleGui.Show("w600 h580")

; Add instructions
UpdateStatus("Click 'Start Camera' to begin")
